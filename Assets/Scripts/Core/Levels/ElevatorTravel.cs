using System.Diagnostics;
using Consts;
using Cysharp.Threading.Tasks;
using EventGeneration;
using MalteHusung.GlobalMessage;
using Unity.Netcode;
using UnityEngine;
using Utils.Debug;

[HelpURL(StringConsts.DocumentationLink + "NT-A-21")]
public class ElevatorTravel : NetworkBehaviour
{
    #region Debug

    private static DebugLevel DebugLevel => DebugMessengerProvider.GetLevel(type, DebugLevel.Warning);
    private static readonly System.Type type = typeof(ElevatorTravel);

    #endregion

    [SerializeField]
    private float minTravelTimeSeconds = 5f;

    [SerializeField]
    private float maxTravelTimeSeconds = 10f;

    private bool _isTravelling;
    private readonly Stopwatch _travelStopwatch = new();

    // SRV context
    private async void Travel()
    {
        if (_isTravelling)
            return;

        _isTravelling = true;

        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"Travel start");

        InSceneNetworkBehaviourManager.Instance.ResetSpawnStatus();
        _travelStopwatch.Reset();

        // true = GameState.StartRoom; false = GameState.LocationGameplay
        var isLeavingStartRoom = GameStateTracker.Instance.CurrentState == GameStateTracker.GameState.StartRoom;

        var departingFrom = isLeavingStartRoom ? GameStateTracker.GameState.DepartingFromStartRoom : GameStateTracker.GameState.DepartingFromLocation;
        GameStateTracker.Instance.SwitchCurrentGameState(departingFrom);

        // close elevator doors
        await UniTask.Delay(NumericConsts.ElevatorDoorsAnimationDurationMS);

        _travelStopwatch.Start();

        if (isLeavingStartRoom)
        {
            GameStateTracker.Instance.SwitchCurrentGameState(GameStateTracker.GameState.TravellingToLocation);

#if UNLOAD_START_ROOM
            {
                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                    DebugMessenger.Message("Unload Start Room");

                var unloadStartRoomStartStamp = _travelStopwatch.ElapsedMilliseconds;

                await MultiplayerManager.Instance.UnloadSceneWhenReady(UnityEngine.SceneManagement.SceneManager.GetSceneByName(StringConsts.StartRoom));

                var unloadStartRoomTime = _travelStopwatch.ElapsedMilliseconds - unloadStartRoomStartStamp;

                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                    DebugMessenger.Message($"Start room unloaded in [{unloadStartRoomTime}ms]");
            }
#endif

            await UniTask.Delay(500);

            {
                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                    DebugMessenger.Message("Load Location");

                var loadLocationStartStamp = _travelStopwatch.ElapsedMilliseconds;

                await LevelManager.Instance.RequestLoadNextLevel();

                var loadLocationTime = _travelStopwatch.ElapsedMilliseconds - loadLocationStartStamp;

                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                    DebugMessenger.Message($"Location loaded in [{loadLocationTime}ms]");
            }

            await UniTask.Delay(1500);

            {
                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                    DebugMessenger.Message("Remember values");

                SessionStatsManager.Instance.RememberRemainingWorkValues();
            }
        }
        else
        {
            GameStateTracker.Instance.SwitchCurrentGameState(GameStateTracker.GameState.TravellingToStartRoom);

            {
                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                    DebugMessenger.Message("Unload Location");

                var unloadLocationStartStamp = _travelStopwatch.ElapsedMilliseconds;

                await LevelManager.Instance.RequestUnloadCurrentLevel();

                var unloadLocationTime = _travelStopwatch.ElapsedMilliseconds - unloadLocationStartStamp;

                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                    DebugMessenger.Message($"Location unloaded in [{unloadLocationTime}ms]");
            }

            await UniTask.Delay(500);

#if UNLOAD_START_ROOM
            {
                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                    DebugMessenger.Message("Load Start Room");

                var loadStartRoomStartStamp = _travelStopwatch.ElapsedMilliseconds;

                await MultiplayerManager.Instance.LoadSceneWhenReady(StringConsts.StartRoom);

                var loadStartRoomTime = _travelStopwatch.ElapsedMilliseconds - loadStartRoomStartStamp;

                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                    DebugMessenger.Message($"Start Room loaded in [{loadStartRoomTime}ms]");
            }
#endif
        }

        var minimumElevatorTravelTimeSeconds = GameConfig.Instance.DebugSettings.ZeroMinimumTravelTime ? 0f : Random.Range(minTravelTimeSeconds, maxTravelTimeSeconds);
        var spareTime = minimumElevatorTravelTimeSeconds * 1000 - _travelStopwatch.ElapsedMilliseconds;
        await UniTask.Delay(Mathf.Max(500, (int)spareTime));

        _travelStopwatch.Stop();

        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"Travel duration : [{_travelStopwatch.ElapsedMilliseconds}ms]");

        var arrivingTo = isLeavingStartRoom ? GameStateTracker.GameState.ArrivingToLocation : GameStateTracker.GameState.ArrivingToStartRoom;

        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"Switch to {arrivingTo}");

        while (IterativeSceneLoader.Instance != null && !IterativeSceneLoader.Instance.IsGenerationCompleted)
        {
            await UniTask.Delay(500);
        }

        GameStateTracker.Instance.SwitchCurrentGameState(arrivingTo);

        // open elevator doors
        await UniTask.Delay(NumericConsts.ElevatorDoorsAnimationDurationMS);

        var arrivedTo = isLeavingStartRoom ? GameStateTracker.GameState.LocationGameplay : GameStateTracker.GameState.StartRoom;

        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"Switch to {arrivedTo}");

        GameStateTracker.Instance.SwitchCurrentGameState(arrivedTo);

        _travelStopwatch.Reset();

        _isTravelling = false;
    }

    private void OnEnable()
    {
        MessageType.AddReceiver<ElevatorButtonPressedMessage>(OnTravelButtonPressed);
        MessageType.AddReceiver<TimerRedlineMessage>(OnTravelButtonPressed);
    }

    private void OnDisable()
    {
        MessageType.RemoveReceiver<ElevatorButtonPressedMessage>(OnTravelButtonPressed);
        MessageType.RemoveReceiver<TimerRedlineMessage>(OnTravelButtonPressed);
    }

    private void OnTravelButtonPressed()
    {
        Travel();
    }

#if UNITY_EDITOR

    private void Update()
    {
        if (_travelStopwatch.Elapsed.TotalSeconds > 20f)
            if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Warning))
                DebugMessenger.Message($"Travel time is taking too long");
    }

#endif
}