using System.Collections;
using Unity.Netcode;
using UnityEngine;

// Is not "NetworkSpawn"-ed as network object, but my guess is that
// clients' DecalsManagers are being synchronized via so called "soft synchronization",
// because they are also spawned before any actual network connection and thus must be treated as in-scene network objects
// https://docs-multiplayer.unity3d.com/netcode/current/basics/scenemanagement/inscene-placed-networkobjects/

// In-order reliable RPC execution is guaranteed per NetworkObject basis only.
// https://docs-multiplayer.unity3d.com/netcode/current/advanced-topics/message-system/reliability/

public abstract class NetworkBehaviorManager<TInterface> : NetworkBehaviour
{
    public static TInterface Instance
    {
        get;
        protected set;
    }

    private void Awake()
    {
        if (Instance == null)
        {
            SetInstance();
            Setup();
            DontDestroyOnLoad(gameObject);

            StartCoroutine(LateAwake());
        }
        else
            Destroy(gameObject);
    }

    private IEnumerator LateAwake()
    {
        yield return new WaitForEndOfFrame();
        SetupLate();
    }

    /// <summary>
    /// <see cref="Instance"/> = <see cref="this"/>;
    /// </summary>
    protected abstract void SetInstance();

    protected virtual void Setup() { }
    protected virtual void SetupLate() { }
}
