using System.Collections.Generic;
using System.Linq;
using Consts;
using Unity.Netcode;
using Utils.Debug;

public interface IInSceneNetworkBehaviourManager
{
    bool AreAllClientsFullySpawned { get; }
    void ResetSpawnStatus();
    void Add(NetworkObject networkObject);
    void Remove(NetworkObject networkObject);
    void CheckAllBehavioursSpawned();
    void ConfirmationRoutine(HashSet<ulong> clientsContainer, ulong clientID, NetworkVariable<bool> networkBool, string message);
    void ResetConfirmationPair(HashSet<ulong> clientsContainer, NetworkVariable<bool> networkBool);
}

public class InSceneNetworkBehaviourManager : NetworkBehaviorManager<IInSceneNetworkBehaviourManager>, IInSceneNetworkBehaviourManager
{
    #region Debug

    private static DebugLevel DebugLevel => DebugMessengerProvider.GetLevel(type, DebugLevel.Warning);
    private static readonly System.Type type = typeof(InSceneNetworkBehaviour);

    #endregion

    public bool AreAllClientsFullySpawned => _areAllClientsFullySpawned.Value;
    private readonly NetworkVariable<bool> _areAllClientsFullySpawned = new(false);
    private readonly HashSet<NetworkObject> _levelNetworkBehaviours = new();
    private readonly HashSet<ulong> _fullySpawnedClientsIds = new(NumericConsts.MaxPlayers);

    private bool AllBehavioursSpawned => _levelNetworkBehaviours.All(b => b.IsSpawned);

    public void CheckAllBehavioursSpawned()
    {
        if (AllBehavioursSpawned)
            AllInSceneBehavioursAreSpawnedServerRPC(NetworkManager.LocalClientId);
    }

    protected override void SetInstance()
    {
        Instance = this;
    }

    public void Add(NetworkObject networkObject)
    {
        _levelNetworkBehaviours.Add(networkObject);
    }

    public void Remove(NetworkObject networkObject)
    {
        _levelNetworkBehaviours.Remove(networkObject);
    }

    [ServerRpc(RequireOwnership = false)]
    private void AllInSceneBehavioursAreSpawnedServerRPC(ulong clientID)
    {
        ConfirmationRoutine(_fullySpawnedClientsIds, clientID, _areAllClientsFullySpawned, "All required in-scene NetworkBehaviours spawned");
    }

    public void ConfirmationRoutine(HashSet<ulong> clientsContainer, ulong clientID, NetworkVariable<bool> networkBool, string message)
    {
        if (!clientsContainer.Add(clientID))
            return;

        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"[Server]: Client[{clientID}] confirmed {message}");

        if (NetworkManager.Singleton.ConnectedClients.Keys.Any(connectedClientId => !clientsContainer.Contains(connectedClientId)))
            return;

        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"[Server]: Everyone confirmed {message}");

        networkBool.Value = true;
    }

    public void ResetSpawnStatus()
    {
        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"[Server]: Reset AllClientsFullySpawned status");

        ResetConfirmationPair(_fullySpawnedClientsIds, _areAllClientsFullySpawned);
        _levelNetworkBehaviours.Clear();
    }

    public void ResetConfirmationPair(HashSet<ulong> clientsContainer, NetworkVariable<bool> networkBool)
    {
        clientsContainer.Clear();
        networkBool.Value = false;
    }
}