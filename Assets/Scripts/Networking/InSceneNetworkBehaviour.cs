using System;
using System.Collections.Generic;
using System.Linq;
using Consts;
using Unity.Netcode;
using Utils.Debug;

public class InSceneNetworkBehaviour : NetworkBehaviour
{
    private bool _isLevelBehaviour;

    private void Awake()
    {
        _isLevelBehaviour = gameObject.scene.name.Contains(StringConsts.Level, StringComparison.InvariantCultureIgnoreCase);

        if (_isLevelBehaviour)
            InSceneNetworkBehaviourManager.Instance.Add(NetworkObject);
    }

    public override void OnNetworkSpawn()
    {
        base.OnNetworkSpawn();

        InSceneNetworkBehaviourManager.Instance.CheckAllBehavioursSpawned();
    }

    public override void OnDestroy()
    {
        if (_isLevelBehaviour)
            InSceneNetworkBehaviourManager.Instance.Remove(NetworkObject);

        base.OnDestroy();
    }
}
